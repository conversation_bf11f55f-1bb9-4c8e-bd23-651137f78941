
using ICSharpCode.AvalonEdit.Document;
using ICSharpCode.AvalonEdit.Folding;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;

namespace avalontest
{
    public class SqlFoldingStrategy
    {
        private int searchStartBlock(string text, int startOffset, ref int matchLength, ref string endBlock)
        {
            int minMatchIndex = -1;
            var regex = new Regex(@"\b(PROCEDURE|FUNCTION)[\s\r\n]+(\w+)[\s\S]*?\b(IS|AS)\b", RegexOptions.IgnoreCase);
            var match = regex.Match(text.Substring(startOffset));
            int matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                matchLength = match.Length;
                endBlock = match.Value + "{}" + $@"\bEND(?:[\s\r\n]*;|[\s\r\n]+{match.Groups[2].Value}[\s\r\n]*;)";
            }

            regex = new Regex(@"\b(BEGIN)\b+", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                matchLength = match.Length;
                endBlock = match.Value + "{}" + @"\bEND[\s\r\n]*;";
            }

            regex = new Regex(@"\bIF\b[\s\S]*?\bTHEN\b", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                matchLength = match.Length;
                endBlock = match.Value + "{}" + @"\bEND\s+IF\b\s*;";
            }

            regex = new Regex(@"\bWHILE\b[\s\S]*?\bLOOP\b", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                matchLength = match.Length;
                endBlock = match.Value + "{}" + @"\bEND\s+LOOP\b\s*;";
            }

            regex = new Regex(@"\b(LOOP)\b", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            if (match.Success)
            {
                regex = new Regex(@"\bEND\b[\s\S]*?\bLOOP\b", RegexOptions.IgnoreCase);
                var match2 = regex.Match(text.Substring(startOffset));
                if (match2.Success)
                {
                    if (!(match.Index > match2.Index && match.Index + match.Length <= match2.Index + match2.Length))
                    {
                        matchIndex = match.Success ? startOffset + match.Index : -1;
                        if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
                        {
                            minMatchIndex = matchIndex;
                            matchLength = match.Length;
                            endBlock = match.Value + "{}" + @"\bEND\s+LOOP\b\s*;";
                        }
                    }
                }
            }
            return minMatchIndex;
        }
        private int searchFolding(string text, int startFrom, NewFolding folding, List<NewFolding> foldings)
        {
            string endBlock = string.Empty;
            int matchLength = 0;
            var beginP = searchStartBlock(text, startFrom, ref matchLength, ref endBlock); //傳回text中的絕對位置

            string pattern = folding.Name.Substring(folding.Name.IndexOf("{}") + 2);
            var regex = new Regex(pattern, RegexOptions.IgnoreCase); // 搜尋上層區塊的 結束標記
            var match = regex.Match(text.Substring(startFrom));
            int endP = match.Success ? startFrom + match.Index : -1;
            if (endP > 0)
            {
                folding.EndOffset = endP + match.Length - 1;
            }

            if ((endP < beginP || beginP < 0) && endP > 0)
            { //上層區塊的 結束標記 先找到
                folding.EndOffset = endP + match.Length - 1;
                return folding.EndOffset;
            }
            if (endP < 0 && beginP < 0)
            {
                folding.EndOffset = folding.StartOffset + folding.Name.IndexOf("{}") - 3;
                return folding.EndOffset;
            }
            if (beginP >= 0)
            {
                var newFolding = new NewFolding(beginP, beginP + matchLength)
                {
                    Name = endBlock
                };
                foldings.Add(newFolding);
                var newP = searchFolding(text, beginP + matchLength, newFolding, foldings); //往下一層比對

                //下層比對返回，位置已經變動，再重新比對本層的結束標記
                if (newP >= folding.EndOffset)
                {
                    match = regex.Match(text.Substring(newP));
                    endP = match.Success ? newP + match.Index : -1;
                    if (endP >= 0)
                    {
                        folding.EndOffset = endP + match.Length - 1;
                    }
                    else
                    {
                        folding.EndOffset = text.Length - 1;
                    }
                }
                return folding.EndOffset;
            }
            return text.Length;
        }

        public IEnumerable<NewFolding> CreateNewFoldings(TextDocument document, out int firstErrorOffset)
        {
            firstErrorOffset = -1;
            var text = document.Text;
            var foldings = new List<NewFolding>();

            var regex = new Regex(@"\bEND[\s\r\n]*;", RegexOptions.IgnoreCase);
            var match = regex.Match(@"END;");
            //var pattern = @"\bEND(?:[\s\r\n]*;|[\s\r\n]+MYNAME[\s\r\n]*;)";
            //regex = new Regex(pattern, RegexOptions.IgnoreCase);
            //match = regex.Match("END   MYname ;");

            //var test = match.Success;
            int matchLength = 0;
            var folding = new NewFolding(0, 0);
            string endBlock = string.Empty;
            var block0 = searchStartBlock(text, 0, ref matchLength, ref endBlock);
            if (block0 >= 0) {
                folding.StartOffset = block0;
                folding.Name = endBlock;
                foldings.Add(folding);
                searchFolding(text, block0 + matchLength, folding, foldings);
            }
            foreach (var f in foldings)
            {
                f.Name = f.Name.Substring(0, f.Name.IndexOf("{}")).Replace("\r", "").Replace("\n", "");
                var line = document.GetLineByOffset(f.EndOffset);
                Debug.WriteLine($"Line start: {line.Offset}, end: {line.EndOffset}, delimiterLength: {line.EndOffset - line.DelimiterLength}");
                if (line.EndOffset > line.EndOffset - line.DelimiterLength + 1)
                {
                    f.EndOffset = line.EndOffset - line.DelimiterLength + 1;
                }
            }
            return foldings;
        }

        /// <summary>
        /// Updates the foldings in the folding manager.
        /// </summary>
        public void UpdateFoldings(FoldingManager manager, TextDocument document)
        {
            int firstErrorOffset;
            var newFoldings = CreateNewFoldings(document, out firstErrorOffset);
            manager.UpdateFoldings(newFoldings, firstErrorOffset);
        }
    }
}
