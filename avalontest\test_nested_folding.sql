-- 測試巢狀摺疊的 PL/SQL 程式碼
CREATE OR REPLACE PACKAGE test_package AS
    PROCEDURE outer_proc(p_id NUMBER);
END test_package;
/

CREATE OR REPLACE PACKAGE BODY test_package AS
    PROCEDURE outer_proc(p_id NUMBER) IS
        v_counter NUMBER := 0;
    BEGIN
        -- 外層 IF
        IF p_id > 0 THEN
            -- 內層 FOR 迴圈
            FOR i IN 1..10 LOOP
                -- 更內層的 IF
                IF i MOD 2 = 0 THEN
                    -- CASE 語句
                    CASE i
                        WHEN 2 THEN
                            DBMS_OUTPUT.PUT_LINE('Two');
                        WHEN 4 THEN
                            DBMS_OUTPUT.PUT_LINE('Four');
                        ELSE
                            DBMS_OUTPUT.PUT_LINE('Other even');
                    END CASE;
                ELSE
                    /* 多行註解
                       在這裡 */
                    DBMS_OUTPUT.PUT_LINE('Odd number: ' || i);
                END IF;
                
                v_counter := v_counter + 1;
            END LOOP;
        ELSE
            -- 另一個巢狀結構
            WHILE v_counter < 5 LOOP
                IF v_counter = 3 THEN
                    EXIT;
                END IF;
                v_counter := v_counter + 1;
            END LOOP;
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Error: ' || SQLERRM);
    END outer_proc;
END test_package;
/