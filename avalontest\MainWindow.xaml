﻿<Window x:Class="avalontest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
        xmlns:local="clr-namespace:avalontest"
        mc:Ignorable="d"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- Toolbar -->
            <RowDefinition Height="1*"/>
            <!-- SQL Editor -->
            <RowDefinition Height="3"/>
            <!-- Splitter -->
            <RowDefinition Height="1*"/>
            <!-- Results and TreeView -->
            <RowDefinition Height="Auto"/>
            <!-- Status Bar -->
        </Grid.RowDefinitions>
        
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
            <avalonedit:TextEditor x:Name="SqlEditor"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   ShowLineNumbers="True"
                                   WordWrap="False"
                                   HorizontalScrollBarVisibility="Auto"
                                   VerticalScrollBarVisibility="Auto"
                                   Background="White"
                                   Foreground="Black">
                <avalonedit:TextEditor.Options>
                    <avalonedit:TextEditorOptions ShowSpaces="False"
                                                  ShowTabs="False"
                                                  ShowEndOfLine="False"
                                                  ShowBoxForControlCharacters="False"
                                                  ConvertTabsToSpaces="True"
                                                  IndentationSize="4"/>
                </avalonedit:TextEditor.Options>
            </avalonedit:TextEditor>
        </Border>
        
        <!-- GridSplitter for resizing -->
        <GridSplitter Grid.Row="2" 
                      Height="3" 
                      HorizontalAlignment="Stretch" 
                      VerticalAlignment="Center" 
                      Background="#FFBCBCBC"/>
        
        <!-- TreeView with connecting lines in row 3 -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1,0,1,1">
                <TreeView x:Name="ObjectTreeView" Background="White">
                    <!-- Enable TreeView lines -->
                    <TreeView.Resources>
                        <!-- Converters -->
                        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                        
                        <!-- Modern ToggleButton Style with Chevron -->
                        <Style x:Key="ExpandCollapseToggleStyle" TargetType="ToggleButton">
                            <Setter Property="Focusable" Value="False"/>
                            <Setter Property="Width" Value="16"/>
                            <Setter Property="Height" Value="16"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border Width="16" Height="16" Background="Transparent">
                                            <Path x:Name="ExpandPath"
                                                  Stroke="#FF707070"
                                                  StrokeThickness="1.5"
                                                  RenderTransformOrigin="0.5,0.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M 4 6 L 8 10 L 12 6"/>
                                                </Path.Data>
                                                <Path.RenderTransform>
                                                    <RotateTransform Angle="0"/>
                                                </Path.RenderTransform>
                                            </Path>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="ExpandPath" Property="RenderTransform">
                                                    <Setter.Value>
                                                        <RotateTransform Angle="90"/>
                                                    </Setter.Value>
                                                </Setter>
                                            </Trigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Stroke" TargetName="ExpandPath" Value="#FF3399FF"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>

                        <!-- Style for TreeViewItem with dotted connecting lines -->
                        <Style x:Key="TreeViewItemStyle" TargetType="{x:Type TreeViewItem}">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="{x:Type TreeViewItem}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" MinWidth="19"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <!-- Dotted Connecting Lines -->
                                            <Line x:Name="HorLine"
                                                  Stroke="#FFBCBCBC"
                                                  StrokeDashArray="2 2"
                                                  X1="0" Y1="0" X2="10" Y2="0"
                                                  Margin="9,0,0,0"
                                                  SnapsToDevicePixels="True"
                                                  HorizontalAlignment="Left"
                                                  VerticalAlignment="Center"/>

                                            <Line x:Name="VerLine"
                                                  Stroke="#FFBCBCBC"
                                                  StrokeDashArray="2 2"
                                                  X1="0" Y1="0" X2="0" Y2="10"
                                                  SnapsToDevicePixels="True"
                                                  Grid.RowSpan="2"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Top"/>

                                            <ToggleButton x:Name="Expander"
                                                          Style="{StaticResource ExpandCollapseToggleStyle}"
                                                          ClickMode="Press"
                                                          IsChecked="{Binding IsExpanded, RelativeSource={RelativeSource TemplatedParent}}"
                                                          Margin="0,0,0,0" Grid.Row="0" Grid.Column="0"/>

                                            <Border x:Name="Bd"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    Background="{TemplateBinding Background}"
                                                    Grid.Column="1"
                                                    Padding="{TemplateBinding Padding}"
                                                    SnapsToDevicePixels="True"
                                                    Grid.Row="0">
                                                <ContentPresenter x:Name="PART_Header"
                                                                  ContentSource="Header"
                                                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                                            </Border>

                                            <ItemsPresenter x:Name="ItemsHost" Grid.Column="1" Grid.Row="1" Margin="0,2,0,0"/>
                                            
                                            <!-- Vertical line for child items -->
                                            <Line x:Name="ChildVerLine"
                                                  Grid.Row="1"
                                                  Grid.Column="0"
                                                  Stroke="#FFBCBCBC"
                                                  StrokeDashArray="2 2"
                                                  X1="0" Y1="0" X2="0" Y2="10"
                                                  Stretch="Fill"
                                                  HorizontalAlignment="Center"
                                                  SnapsToDevicePixels="True"/>
                                        </Grid>

                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsExpanded" Value="False">
                                                <Setter Property="Visibility" TargetName="ItemsHost" Value="Collapsed"/>
                                                <Setter Property="Visibility" TargetName="ChildVerLine" Value="Collapsed"/>
                                            </Trigger>
                                            <Trigger Property="HasItems" Value="False">
                                                <Setter Property="Visibility" TargetName="Expander" Value="Hidden"/>
                                                <Setter Property="Visibility" TargetName="HorLine" Value="Hidden"/>
                                                <Setter Property="Visibility" TargetName="VerLine" Value="Hidden"/>
                                                <Setter Property="Visibility" TargetName="ChildVerLine" Value="Hidden"/>
                                            </Trigger>
                                            <MultiTrigger>
                                                <MultiTrigger.Conditions>
                                                    <Condition Property="HasHeader" Value="False"/>
                                                    <Condition Property="Width" Value="Auto"/>
                                                </MultiTrigger.Conditions>
                                                <Setter Property="MinWidth" TargetName="Bd" Value="75"/>
                                            </MultiTrigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" TargetName="Bd" Value="#FFD8ECFF"/>
                                                <Setter Property="Foreground" Value="Black"/>
                                            </Trigger>
                                            <Trigger Property="IsEnabled" Value="False">
                                                <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Foreground" Value="#FF333333"/>
                            <Setter Property="Padding" Value="2"/>
                        </Style>
                    </TreeView.Resources>
                    
                    <!-- 使用 ItemContainerStyle 將樣式套用到所有層級的 TreeViewItem -->
                    <TreeView.ItemContainerStyle>
                        <Style TargetType="{x:Type TreeViewItem}" BasedOn="{StaticResource TreeViewItemStyle}"/>
                    </TreeView.ItemContainerStyle>
                    
                    <!-- Sample TreeView Items -->
                    <TreeViewItem Header="資料庫物件" IsExpanded="True">
                        <TreeViewItem Header="資料表" IsExpanded="True">
                            <TreeViewItem Header="客戶資料表"/>
                            <TreeViewItem Header="訂單資料表"/>
                            <TreeViewItem Header="產品資料表"/>
                        </TreeViewItem>
                        <TreeViewItem Header="檢視表">
                            <TreeViewItem Header="客戶訂單檢視"/>
                            <TreeViewItem Header="產品庫存檢視"/>
                        </TreeViewItem>
                        <TreeViewItem Header="預存程序">
                            <TreeViewItem Header="新增客戶"/>
                            <TreeViewItem Header="處理訂單"/>
                            <TreeViewItem Header="更新庫存"/>
                        </TreeViewItem>
                        <TreeViewItem Header="函數">
                            <TreeViewItem Header="計算總額"/>
                            <TreeViewItem Header="驗證客戶"/>
                        </TreeViewItem>
                    </TreeViewItem>
                </TreeView>
            </Border>
            
            <!-- Content area for results -->
            <Border Grid.Column="1" BorderBrush="Gray" BorderThickness="0,0,1,1">
                <TabControl>
                    <TabItem Header="結果">
                        <DataGrid x:Name="ResultsGrid" AutoGenerateColumns="True"/>
                    </TabItem>
                    <TabItem Header="訊息">
                        <TextBox x:Name="MessagesTextBox" IsReadOnly="True" TextWrapping="Wrap" 
                                 VerticalScrollBarVisibility="Auto"/>
                    </TabItem>
                </TabControl>
            </Border>
        </Grid>
        
    </Grid>
</Window>
