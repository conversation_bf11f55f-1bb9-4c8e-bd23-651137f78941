﻿using System;
using System.Collections.ObjectModel;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using ICSharpCode.AvalonEdit.Folding;
using System.Xml;

namespace avalontest
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private FoldingManager foldingManager;
        private SqlFoldingStrategy foldingStrategy;
        private DispatcherTimer foldingUpdateTimer;
        public MainWindow()
        {
            InitializeComponent();
            SqlEditor.SyntaxHighlighting = HighlightingManager.Instance.GetDefinition("SQL");
            
            foldingManager = FoldingManager.Install(SqlEditor.TextArea);
            foldingStrategy = new SqlFoldingStrategy();

            // 初始化防抖動計時器
            foldingUpdateTimer = new DispatcherTimer();
            foldingUpdateTimer.Interval = TimeSpan.FromSeconds(2);
            foldingUpdateTimer.Tick += FoldingUpdateTimer_Tick;

            SqlEditor.Document.TextChanged += SqlEditor_TextChanged;
            UpdateFolding();
            
            // 初始化 TreeView 的示例數據
            InitializeTreeView();
        }
        
        private void InitializeTreeView()
        {
            // 這裡可以添加更多的 TreeView 項目或從數據庫加載實際的對象
            // 目前使用 XAML 中定義的靜態項目
            
            // 為 TreeView 添加選擇事件處理
            var treeView = this.FindName("ObjectTreeView") as TreeView;
            if (treeView != null)
            {
                treeView.SelectedItemChanged += ObjectTreeView_SelectedItemChanged;
            }
        }
        
        private void ObjectTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            var selectedItem = e.NewValue as TreeViewItem;
            if (selectedItem != null)
            {
                // 這裡可以處理選擇項目的邏輯
                // 例如，顯示選定對象的詳細信息或在編輯器中生成 SQL
                string itemName = selectedItem.Header?.ToString() ?? "";
                
                var messagesTextBox = this.FindName("MessagesTextBox") as TextBox;
                if (messagesTextBox != null)
                {
                    messagesTextBox.Text = $"已選擇: {itemName}";
                }
            }
        }

        private void SqlEditor_TextChanged(object? sender, EventArgs e)
        {
            // 重置計時器 - 如果已經在運行，停止並重新開始
            foldingUpdateTimer.Stop();
            foldingUpdateTimer.Start();
        }

        private void FoldingUpdateTimer_Tick(object? sender, EventArgs e)
        {
            // 停止計時器並執行 UpdateFolding
            foldingUpdateTimer.Stop();
            UpdateFolding();
        }

        private void UpdateFolding()
        {
            if (foldingStrategy != null)
            {
                foldingStrategy.UpdateFoldings(foldingManager, SqlEditor.Document);
            }
        }
    }
}
